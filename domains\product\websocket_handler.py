"""
WebSocket Handler for Product Pickup Sessions.
Handles WebSocket communication for product pickup operations using pickup_process.
"""

import logging
import json
from fastapi import WebSocket, WebSocketDisconnect
from typing import Optional
import asyncio

from managers.ws_manager import ws_manager
from managers.session_manager import session_manager

# Global message queue management now handled by payment_manager

logger = logging.getLogger(__name__)

# Old flow-based handler removed - flow coordinator system deleted

async def handle_product_pickup_websocket(
    websocket: WebSocket,
    session_id: str
):
    """Handle WebSocket communication for product pickup sessions using pickup_process"""

    logger.info(f"Product pickup WebSocket connected: {session_id}")

    try:
        # Register WebSocket connection
        await ws_manager.connect(session_id, websocket)
        logger.info(f"Registered WebSocket connection for product pickup session: {session_id}")

        # Get session data
        session = session_manager.get_session(session_id)
        if not session:
            logger.error(f"No session found for session_id: {session_id}")
            await ws_manager.send(session_id, {
                "type": "error",
                "message": "Session not found"
            })
            return

        section_id = getattr(session, 'section_id', None)
        requires_payment = getattr(session, 'amount', 0) > 0
        product_id = getattr(session, 'product_id', None)

        if not section_id:
            logger.error(f"No section_id found in session {session_id}")
            await ws_manager.send(session_id, {
                "type": "error",
                "message": "Invalid session data"
            })
            return

        logger.info(f"Starting product pickup for section {section_id}, requires_payment: {requires_payment}")

        # Create message queue for pickup_process
        message_queue = asyncio.Queue()

        # Message handler task
        async def handle_websocket_messages():
            try:
                while True:
                    message = await websocket.receive_text()
                    logger.info(f"Received WebSocket message for session {session_id}: {message}")

                    try:
                        data = json.loads(message)
                        await message_queue.put(data)
                    except json.JSONDecodeError as e:
                        logger.error(f"Invalid JSON message: {e}")
                        await ws_manager.send(session_id, {
                            "type": "error",
                            "message": "Invalid JSON format"
                        })
            except WebSocketDisconnect:
                logger.info(f"WebSocket disconnected for session {session_id}")
            except Exception as e:
                logger.error(f"Error in message handler: {e}")

        # Start message handler task
        message_task = asyncio.create_task(handle_websocket_messages())

        # PAYMENT STEP - Use payment_manager for payment handling if required
        if requires_payment:
            logger.info("Payment required, using payment_manager for payment")

            from managers.payment_manager import payment_process_with_callbacks
            payment_success = await payment_process_with_callbacks(
                session_id=session_id,
                amount=getattr(session, 'amount', 0),
                message_queue=message_queue
            )

            if not payment_success:
                logger.info("Payment failed or cancelled - ending pickup process")
                return

            logger.info("Payment completed successfully - proceeding to pickup")

        # Start pickup_process directly from universal process_manager
        from managers.process_manager import pickup_process
        success, successful_sections = await pickup_process(
            sections=[section_id],
            session_id=session_id,
            message_queue=message_queue,
            requires_payment=False  # Payment already handled above
        )

        # Cancel message handler
        message_task.cancel()

        # Debug logging
        logger.info(f"Pickup process results - success: {success}, successful_sections: {successful_sections}, product_id: {product_id}")

        # Complete product pickup operation
        # if success and successful_sections and product_id:
        #     # Mark product as picked up in database (status=0 means completed/picked up)
        #     try:
        #         from infrastructure.repositories.product_repository import ProductRepository
        #         repo = ProductRepository()
        #         repo.update_sale_reservation_status(product_id, 0)
        #         logger.info(f"Marked product {product_id} as picked up after successful pickup")
        #     except Exception as e:
        #         logger.error(f"Error marking product as picked up: {e}")
        # else:
        #     logger.warning(f"Skipping database update - success: {success}, successful_sections: {successful_sections}, product_id: {product_id}")

        logger.info(f"Product pickup completed for session {session_id}: success={success}")

    except WebSocketDisconnect:
        logger.info(f"Product pickup WebSocket disconnected: {session_id}")
    except Exception as e:
        logger.error(f"Error in product pickup WebSocket handler: {e}")
        try:
            await ws_manager.send(session_id, {
                "type": "error",
                "message": f"Error in pickup process: {str(e)}"
            })
        except:
            pass
    finally:
        # Cleanup WebSocket connection
        ws_manager.disconnect(session_id)
        logger.info(f"Product pickup WebSocket connection closed: {session_id}")
