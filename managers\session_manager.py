"""
Unified session management for both FSM sequences and backend operations.
Handles session creation, validation, state management across the entire system.
"""

import asyncio
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import logging
from pydantic import BaseModel

logger = logging.getLogger(__name__)

class SessionType:
    """Session type constants"""
    FSM_SEQUENCE = "fsm_sequence"
    BACKEND_SALE = "backend_sale"
    BACKEND_PICKUP = "backend_pickup"
    BACKEND_STORAGE = "backend_storage"
    PAYMENT = "payment"  # Deprecated - replaced by TRANSACTION
    TRANSACTION = "transaction"  # Universal transaction session (payment, storage, etc.)
    PRODUCT_FLOW = "product_flow"  # New flow-based product sessions
    STORAGE_FLOW = "storage_flow"  # New flow-based storage sessions
    ORDER_FLOW = "order_flow"  # New flow-based order sessions
    OPERATOR_FSM = "operator_fsm"  # Operator FSM sequences (no door closing required)
    STORAGE_PICKUP = "storage_pickup"  # Simple storage pickup using pickup_process directly
    PRODUCT_PICKUP = "product_pickup"  # Simple product pickup using pickup_process directly

class SessionStatus:
    """Session status constants"""
    PENDING = "pending"
    ACTIVE = "active"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    EXPIRED = "expired"

class SectionConfig(BaseModel):
    """Configuration for a locker section"""
    section_id: int          # Visual ID shown to user (e.g., "Section 1")
    lock_id: int            # Actual hardware lock pin/output for unlock/lock commands  
    is_tempered: bool
    led_section: Optional[int] = None  # LED ID for this section

class SessionData(BaseModel):
    """Session data model"""
    session_id: str
    session_type: str
    status: str = SessionStatus.PENDING
    created_at: datetime
    expires_at: Optional[datetime] = None

    # FSM specific data
    pin: Optional[str] = None
    sections: Optional[List[SectionConfig]] = None
    current_section_index: Optional[int] = None
    auto_mode: bool = False

    # Backend specific data
    user_data: Optional[Dict[str, Any]] = None
    transaction_data: Optional[Dict[str, Any]] = None

    # Storage flow specific data
    operation: Optional[str] = None
    section_id: Optional[int] = None
    amount: Optional[float] = None
    size_category: Optional[int] = None
    reservation_id: Optional[int] = None
    reservation_pin: Optional[str] = None
    email: Optional[str] = None

    # Endpoint tracking - which endpoint started the FSM sequence
    endpoint_type: Optional[str] = None  # 'product', 'storage', 'order', 'operator', etc.

    # Product flow specific data
    product_id: Optional[int] = None
    product_uuid: Optional[str] = None

    # Order flow specific data
    operator_id: Optional[int] = None
    phone_number: Optional[str] = None
    sections: Optional[List[int]] = None
    insert_pin: Optional[str] = None

    class Config:
        arbitrary_types_allowed = True

class SessionManager:
    """Unified session manager"""
    
    def __init__(self):
        self.active_sessions: Dict[str, SessionData] = {}
        self.default_expiry_minutes = 30
        self.cleanup_task = None
        
    async def start_cleanup_task(self):
        """Start background cleanup task for expired sessions"""
        if self.cleanup_task is None:
            self.cleanup_task = asyncio.create_task(self._cleanup_expired_sessions())
    
    async def stop_cleanup_task(self):
        """Stop background cleanup task"""
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
            self.cleanup_task = None
    
    async def _cleanup_expired_sessions(self):
        """Background task to clean up expired sessions"""
        while True:
            try:
                now = datetime.now()
                expired_sessions = []
                
                for session_id, session in self.active_sessions.items():
                    if session.expires_at and now > session.expires_at:
                        expired_sessions.append(session_id)
                
                for session_id in expired_sessions:
                    logger.info(f"Cleaning up expired session: {session_id}")
                    await self.remove_session(session_id)
                
                # Clean up every 5 minutes
                await asyncio.sleep(300)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in session cleanup: {e}")
                await asyncio.sleep(60)  # Wait a minute before retrying
    
    def create_session(
        self, 
        session_type: str,
        expiry_minutes: Optional[int] = None,
        session_id: Optional[str] = None,
        **kwargs
    ) -> SessionData:
        """
        Create a new session
        
        Args:
            session_type: Type of session (FSM_SEQUENCE, BACKEND_SALE, etc.)
            expiry_minutes: Session expiry in minutes (default: 30)
            session_id: Optional custom session ID (if not provided, will generate UUID)
            **kwargs: Additional session data
            
        Returns:
            SessionData object
        """
        if not session_id:
            session_id = str(uuid.uuid4())
            
        now = datetime.now()
        expiry = now + timedelta(minutes=expiry_minutes or self.default_expiry_minutes)
        
        session = SessionData(
            session_id=session_id,
            session_type=session_type,
            created_at=now,
            expires_at=expiry,
            **kwargs
        )
        
        self.active_sessions[session_id] = session
        logger.info(f"Created {session_type} session: {session_id}")
        
        return session
    
    def get_session(self, session_id: str) -> Optional[SessionData]:
        """Get session by ID"""
        session = self.active_sessions.get(session_id)
        
        if session and session.expires_at and datetime.now() > session.expires_at:
            logger.warning(f"Session {session_id} has expired")
            self.active_sessions.pop(session_id, None)
            return None
            
        return session
    
    def update_session(self, session_id: str, **updates) -> bool:
        """Update session data"""
        session = self.get_session(session_id)
        if not session:
            return False
        
        for key, value in updates.items():
            if hasattr(session, key):
                setattr(session, key, value)
            else:
                logger.warning(f"Unknown session attribute: {key}")
        
        logger.debug(f"Updated session {session_id}: {updates}")
        return True
    
    async def remove_session(self, session_id: str) -> bool:
        """Remove session"""
        if session_id in self.active_sessions:
            session = self.active_sessions.pop(session_id)
            logger.info(f"Removed session: {session_id} (type: {session.session_type})")
            return True
        return False
    
    def get_sessions_by_type(self, session_type: str) -> List[SessionData]:
        """Get all sessions of specific type"""
        return [
            session for session in self.active_sessions.values()
            if session.session_type == session_type
        ]
    
    def extend_session(self, session_id: str, minutes: int = 30) -> bool:
        """Extend session expiry time"""
        session = self.get_session(session_id)
        if not session:
            return False
        
        session.expires_at = datetime.now() + timedelta(minutes=minutes)
        logger.info(f"Extended session {session_id} by {minutes} minutes")
        return True

# Global session manager instance
session_manager = SessionManager()
