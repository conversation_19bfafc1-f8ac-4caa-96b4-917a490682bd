"""
Temperature sensor control interface for electronic and Comet sensors.
Provides clean abstraction for reading temperature data from various sources.
"""

import asyncio
import logging
import requests
import xml.etree.ElementTree as ET
from typing import Dict, List, Optional, Any
from os import getenv
from .electronics_api import send_command
from config import device_config

logger = logging.getLogger(__name__)

class TemperatureController:
    """High-level controller for temperature sensor operations"""

    def __init__(self):
        self.max_retries = device_config.fsm_config.get("max_command_retries", 2)
        self.retry_delay = device_config.fsm_config.get("retry_delay", 1)
        self.mock_mode = device_config.fsm_config.get("box_virtual", False)
        self.temperature_sensor_pins = getenv("TEMPERATURE_SENSOR_PINS", "1,2,3,4,5,6,7,8,9").split(",")
        self.comet_ip_addresses = getenv("COMET_IP_ADDRESSES", "*************,*************").split(",")
        self.comet_timeout = int(getenv("COMET_TIMEOUT", "5"))

    async def get_electronic_sensor_temp(self, sensor_id: int) -> Optional[float]:
        """
        Get temperature from electronic sensor by sensor ID

        Args:
            sensor_id: Sensor ID (channel number)

        Returns:
            Temperature value in Celsius, None if error
        """
        try:
            if self.mock_mode:
                # Return mock temperature for testing
                return 0     # ELECTRONIC MOCK MODE

            # Use boardctl.py read_temperature command with sensor_id as parameter
            result = await send_command(str(sensor_id), "read_temperature", max_retries=self.max_retries)

            if result.startswith('-'):
                logger.error(f"Failed to read temperature from electronic sensor {sensor_id}: {result}")
                return None

            # Parse result - expecting format like "1 4 28.4" (status sensor_id temperature)
            # Status: 1 = success, other values = failure
            parts = result.strip().split()
            if len(parts) >= 3:
                try:
                    status = int(parts[0])
                    returned_sensor_id = int(parts[1])
                    temperature = float(parts[2])

                    # Check if status indicates success (status should be 1)
                    if status != 1:
                        logger.warning(f"Electronic sensor {sensor_id} returned failure status {status}")
                        return None

                    # Verify sensor ID matches
                    if returned_sensor_id != sensor_id:
                        logger.warning(f"Electronic sensor ID mismatch: requested {sensor_id}, got {returned_sensor_id}")
                        return None

                    logger.debug(f"Electronic sensor {sensor_id} temperature: {temperature}°C")
                    return temperature

                except ValueError as e:
                    logger.error(f"Failed to parse temperature response '{result}' from sensor {sensor_id}: {e}")
                    return None
            else:
                logger.error(f"Invalid temperature response format from sensor {sensor_id}: {result} (expected 3 parts)")
                return None

        except Exception as e:
            logger.error(f"Exception reading temperature from electronic sensor {sensor_id}: {e}")
            return None

    async def get_comet_sensors_temp(self, ip: str) -> Optional[float]:
        """
        Get temperature from Comet sensor by IP address

        Args:
            ip: IP address of the Comet sensor

        Returns:
            Temperature value in Celsius, None if error
        """
        try:
            if self.mock_mode:
                # Return mock temperature for testing
                return 0     # ELECTRONIC MOCK MODE

            url = f"http://{ip}/values.xml"

            # Run HTTP request in executor to avoid blocking
            loop = asyncio.get_running_loop()
            response = await loop.run_in_executor(
                None,
                lambda: requests.get(url, timeout=self.comet_timeout)
            )

            if response.status_code == 200:
                # Parse XML response
                root = ET.fromstring(response.text)
                temperature_element = root.find(".//temperature")

                if temperature_element is not None and temperature_element.text:
                    try:
                        temperature = float(temperature_element.text)
                        logger.debug(f"Comet sensor {ip} temperature: {temperature}°C")
                        return temperature
                    except ValueError as e:
                        logger.error(f"Failed to parse temperature value '{temperature_element.text}' from Comet sensor {ip}: {e}")
                        return None
                else:
                    logger.error(f"Temperature element not found in XML response from Comet sensor {ip}")
                    return None
            else:
                logger.error(f"HTTP error {response.status_code} when reading from Comet sensor {ip}")
                return None

        except requests.exceptions.RequestException as e:
            logger.error(f"Network error reading from Comet sensor {ip}: {e}")
            return None
        except ET.ParseError as e:
            logger.error(f"XML parsing error from Comet sensor {ip}: {e}")
            return None
        except Exception as e:
            logger.error(f"Exception reading temperature from Comet sensor {ip}: {e}")
            return None

    async def get_electronic_sensor_temp_all(self) -> List[Dict[str, Any]]:
        """
        Get temperature from all configured electronic sensors
        Only returns sensors with successful readings (status = 1)

        Returns:
            List of sensor data in format:
            [
                {"id": "4", "value": 28.4},
                {"id": "7", "value": 25.1}
            ]
            Note: Failed sensors are not included in the results
        """
        results = []

        for sensor_pin in self.temperature_sensor_pins:
            try:
                sensor_id = int(sensor_pin.strip())
                temperature = await self.get_electronic_sensor_temp(sensor_id)

                # Only include sensors that return valid temperature values
                if temperature is not None:
                    results.append({
                        "id": str(sensor_id),
                        "value": temperature
                    })
                else:
                    logger.debug(f"Skipping electronic sensor {sensor_id} - no valid temperature reading")

            except ValueError:
                logger.error(f"Invalid sensor pin configuration: {sensor_pin}")
                # Don't add invalid sensor pins to results
            except Exception as e:
                logger.error(f"Error reading electronic sensor {sensor_pin}: {e}")
                # Don't add failed sensors to results

        return results

    async def get_comet_sensors_temp_all(self) -> List[Dict[str, Any]]:
        """
        Get temperature from all configured Comet sensors
        Only returns sensors with successful readings

        Returns:
            List of sensor data in format:
            [
                {"id": "*************", "value": 21.3},
                {"id": "*************", "value": 22.1}
            ]
            Note: Failed sensors are not included in the results
        """
        results = []

        for ip_address in self.comet_ip_addresses:
            ip = ip_address.strip()
            try:
                temperature = await self.get_comet_sensors_temp(ip)

                # Only include sensors that return valid temperature values
                if temperature is not None:
                    results.append({
                        "id": ip,
                        "value": temperature
                    })
                else:
                    logger.debug(f"Skipping Comet sensor {ip} - no valid temperature reading")

            except Exception as e:
                logger.error(f"Error reading Comet sensor {ip}: {e}")
                # Don't add failed sensors to results

        return results

# Global controller instance
temperature_controller = TemperatureController()