"""
Pydantic models for sale operations with hardware integration.
Defines request/response models for product management with hardware control.
"""

from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from decimal import Decimal
from datetime import datetime

class ExternalProductText(BaseModel):
    """Text information for product in multiple languages"""
    name: str = ""
    description: str = ""

class ExternalProductTextData(BaseModel):
    """Language-specific text data"""
    en: Optional[ExternalProductText] = None
    cs: ExternalProductText

class ExternalProduct(BaseModel):
    """External API product model"""
    uuid: str
    price: float
    ean: str
    text: ExternalProductTextData
    ageControl: int
    status: int
    createdAt: str
    coverImage: Optional[str] = None

class ExternalProductResponse(BaseModel):
    """External API response model"""
    items: List[ExternalProduct]
    hasMore: bool
    totalCount: int
    success: bool
    message: str

class HardwareConfigResponse(BaseModel):
    """Hardware configuration for a section"""
    section_id: int
    lock_id: int
    is_tempered: bool
    led_section: Optional[int] = None

class ProductInsertRequest(BaseModel):
    """Request model for product insertion"""
    section_id: int
    ean: str

class ProductPickupRequest(BaseModel):
    """Request model for product pickup"""
    section_id: Optional[int] = None
    reservation_pin: Optional[str] = None
    
    def model_post_init(self, __context) -> None:
        """Validate that at least one identifier is provided"""
        if not self.section_id and not self.reservation_pin:
            raise ValueError("Either section_id or reservation_pin must be provided")

class ProductInsertResponse(BaseModel):
    """Response model for product insertion with hardware session"""
    success: bool
    message: str
    product: Optional[Dict[str, Any]] = None
    external_product: Optional[ExternalProduct] = None
    hardware_session_id: Optional[str] = None
    hardware_config: Optional[HardwareConfigResponse] = None
    websocket_url: Optional[str] = None
    error_code: Optional[str] = None

class ProductPickupResponse(BaseModel):
    """Response model for product pickup with flow-based architecture"""
    success: bool
    message: str
    product: Optional[Dict[str, Any]] = None
    websocket_url: Optional[str] = None
    error_code: Optional[str] = None
    
    # Legacy fields for backward compatibility
    hardware_session_id: Optional[str] = None
    hardware_config: Optional[HardwareConfigResponse] = None
    needs_payment: Optional[bool] = None
    payment_session_id: Optional[str] = None
    
    # New flow-based fields
    flow_session_id: Optional[str] = None
    total_steps: Optional[int] = None
    current_step: Optional[str] = None

class BackendSaleSession(BaseModel):
    """Backend sale session information"""
    session_id: str
    session_type: str
    status: str
    created_at: datetime
    expires_at: Optional[datetime] = None
    sections: List[HardwareConfigResponse]
    transaction_data: Optional[Dict[str, Any]] = None

class WebSocketConnectionInfo(BaseModel):
    """Information for WebSocket connection"""
    session_id: str
    websocket_url: str
    session_type: str
    expected_messages: List[str]
    
class SaleWebSocketMessage(BaseModel):
    """Base WebSocket message for sale operations"""
    type: str
    data: Optional[Dict[str, Any]] = None
    message: Optional[str] = None

class ReadyForInsertMessage(SaleWebSocketMessage):
    """WebSocket message indicating readiness for product insertion"""
    type: str = "ready_for_insert"

class ProductInsertedMessage(SaleWebSocketMessage):
    """WebSocket message confirming product was inserted"""
    type: str = "product_inserted"
    section_id: int

class ReadyForPickupMessage(SaleWebSocketMessage):
    """WebSocket message indicating readiness for product pickup"""
    type: str = "ready_for_pickup"

class ProductPickedUpMessage(SaleWebSocketMessage):
    """WebSocket message confirming product was picked up"""
    type: str = "product_picked_up"
    section_id: int
    
class LockerStatusMessage(SaleWebSocketMessage):
    """WebSocket message with locker status updates"""
    type: str = "locker_status"
    section_id: int
    status: str  # "opening", "opened", "closed", "error" 

class FlowPickupResponse(BaseModel):
    """Simple response model for flow-based product pickup"""
    success: bool
    session_id: str
    status: str
    requires_payment: bool
    amount: float
    message: str
    section_id: int