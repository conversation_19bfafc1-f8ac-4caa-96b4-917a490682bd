#!/usr/bin/env python3
import json
import logging
import uuid
import paho.mqtt.client as mqtt

# =============================
# KONFIGURACE - Using connection settings from main.py
# =============================
MQTT_HOST = "*************"
MQTT_PORT = 1883
MQTT_USERNAME = "b-003"  # Must be string for MQTT client
MQTT_PASSWORD = "ECE27m8YMR"  # Must be string for MQTT client
CLIENT_ID = "b-003"
MQTT_CLIENT_ID = f"{CLIENT_ID}-client"
COMMAND_TOPIC = f"devices/{CLIENT_ID}/commands/+/#"
RESPONSE_BASE = f"devices/{CLIENT_ID}/responses"

# =============================
# LOGGING
# =============================
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(message)s")
logger = logging.getLogger(__name__)

# =============================
# CALLBACKY
# =============================
def on_connect(client, userdata, flags, rc):
    if rc == 0:
        logger.info("Connected to broker")
        client.subscribe(COMMAND_TOPIC)
        logger.info(f"Subscribed to {COMMAND_TOPIC}")
    else:
        logger.error(f"Failed to connect, rc={rc}")

def on_message(client, userdata, msg):
    try:
        payload = json.loads(msg.payload.decode())
        topic_parts = msg.topic.split('/')
        command_type = topic_parts[3]          # electronic/system/sale/storage
        command_action = topic_parts[4] if len(topic_parts) > 4 else None

        # připravíme response topic
        response_topic = f"{RESPONSE_BASE}/{command_type}/{command_action}"

        # Validate request_uuid is present
        request_uuid = payload.get("request_uuid")
        if not request_uuid:
            response = {
                "success": False,
                "message": "Missing required field 'request_uuid'",
                "request_uuid": str(uuid.uuid4())  # Generate one for the error response
            }
            client.publish(response_topic, json.dumps(response))
            logger.info(f"Responded on {response_topic}: {response}")
            return

        # ukázková response podle commandu
        response = {"success": True, "request_uuid": request_uuid}

        # elektronika
        if command_type == "electronic":
            response["section_id"] = payload.get("section_id", 1)
            if command_action == "unlock":
                response["message"] = f"Section {response['section_id']} opened successfully"
            elif command_action == "section_open":
                response["message"] = "door was succesfully opened"
            elif command_action == "door_state":
                response["message"] = "door was succesfully checked"
                response.update({"door_state": "open"})  # open, closed, unknown
                
        # system
        elif command_type == "system" and command_action == "reboot_device":
            response["message"] = "Rebooting"

        # sale
        elif command_type == "sale":
            if command_action == "edit_reservation":
                # Validation: Either "product_uuid" or "section_id" must be entered
                if not payload.get("product_uuid") and not payload.get("section_id"):
                    response = {
                        "success": False,
                        "message": "Either 'product_uuid' or 'section_id' must be provided",
                        "request_uuid": request_uuid
                    }
                else:
                    response.update({
                        "message": "Product edited succesfully",
                        "product_uuid": payload.get("product_uuid", "fdsa43"),
                        "sections_id": payload.get("section_id", 3),
                        "status": payload.get("status", 0),
                        "price": payload.get("price", 34)
                    })
            elif command_action == "reserve_product":
                # Validation: Either "product_uuid" or "section_id" must be entered
                if not payload.get("product_uuid") and not payload.get("section_id"):
                    response = {
                        "success": False,
                        "message": "Either 'product_uuid' or 'section_id' must be provided",
                        "request_uuid": request_uuid
                    }
                else:
                    response.update({
                        "message": "Product reserved succesfully",
                        "product_uuid": payload.get("product_uuid", "fdsa43"),
                        "sections_id": payload.get("section_id", 3),
                        "status": 0,
                        "price": 34,
                        "reservation_pin": 123456
                    })
            elif command_action == "unreserve_product":
                # Validation: Either "product_uuid" or "section_id" or "reservation_pin" must be entered
                if not payload.get("product_uuid") and not payload.get("section_id") and not payload.get("reservation_pin"):
                    response = {
                        "success": False,
                        "message": "Either 'product_uuid', 'section_id', or 'reservation_pin' must be provided",
                        "request_uuid": request_uuid
                    }
                else:
                    response.update({
                        "message": "Product unreserved succesfully",
                        "product_uuid": payload.get("product_uuid", "fdsa43"),
                        "sections_id": payload.get("section_id", 3),
                        "status": 0,
                        "price": 34
                    })

        # storage
        elif command_type == "storage" and command_action == "edit_reservation":
            # Validation: Either "reservation_uuid" or "section_id" or "reservation_pin" must be entered
            if not payload.get("reservation_uuid") and not payload.get("section_id") and not payload.get("reservation_pin"):
                response = {
                    "success": False,
                    "message": "Either 'reservation_uuid', 'section_id', or 'reservation_pin' must be provided",
                    "request_uuid": request_uuid
                }
            else:
                response.update({
                    "message": "Storage edited succesfully",
                    "reservation_uuid": payload.get("reservation_uuid", "fdsa43"),
                    "sections_id": payload.get("section_id", 3),
                    "status": payload.get("status", 0),
                    "reservation_pin": payload.get("reservation_pin", 123456)
                })

        client.publish(response_topic, json.dumps(response))
        logger.info(f"Responded on {response_topic}: {response}")

    except Exception as e:
        logger.error(f"Error processing message: {e}")





# =============================
# HLAVNÍ - Using connection approach from main.py
# =============================
def main():
    logger.info("Starting MQTT Client...")
    logger.info(f"Connecting to MQTT broker at {MQTT_HOST}:{MQTT_PORT}")

    # Initialize MQTT client with authentication
    client = mqtt.Client(mqtt.CallbackAPIVersion.VERSION1, client_id=MQTT_CLIENT_ID)

    # Set username and password for authentication
    if MQTT_USERNAME and MQTT_PASSWORD:
        client.username_pw_set(MQTT_USERNAME, MQTT_PASSWORD)
        logger.info(f"Using authentication with username: {MQTT_USERNAME}")

    # Set callbacks
    client.on_connect = on_connect
    client.on_message = on_message

    try:
        # Connect to broker
        client.connect(MQTT_HOST, MQTT_PORT, keepalive=60)

        # Start the network loop (blocking)
        logger.info("Starting MQTT loop...")
        client.loop_forever()

    except KeyboardInterrupt:
        logger.info("Stopping MQTT Client...")
        client.disconnect()
    except Exception as e:
        logger.error(f"Failed to start MQTT client: {e}")

if __name__ == "__main__":
    main()
