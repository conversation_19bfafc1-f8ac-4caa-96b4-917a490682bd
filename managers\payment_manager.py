import asyncio
import logging
import httpx
from datetime import datetime
from typing import Dict

from config import device_config
from managers import ws_manager
from managers.session_manager import session_manager

logger = logging.getLogger(__name__)

# Global registry for active message queues (for universal payment callback injection)
_active_message_queues: Dict[str, asyncio.Queue] = {}


def register_message_queue(session_id: str, message_queue: asyncio.Queue) -> None:
    """
    Register a message queue for payment callback injection.
    Used by all modules (storage, product, order) that need payment callbacks.
    
    Args:
        session_id: Session ID to register queue for
        message_queue: The asyncio.Queue to register
    """
    _active_message_queues[session_id] = message_queue
    logger.info(f"Registered message queue for payment callbacks: {session_id}")


def unregister_message_queue(session_id: str) -> None:
    """
    Unregister a message queue for payment callback injection.
    Should be called when a session ends.
    
    Args:
        session_id: Session ID to unregister queue for
    """
    if session_id in _active_message_queues:
        _active_message_queues.pop(session_id)
        logger.info(f"Unregistered message queue for payment callbacks: {session_id}")


async def inject_payment_callback(session_id: str, status: str, message: str = "") -> bool:
    """
    Universal payment callback injection function.
    Injects payment status directly into the registered message queue for any module.
    
    Args:
        session_id: Session ID to inject callback for
        status: Payment status ("success" or "failed")
        message: Optional message from payment service
        
    Returns:
        bool: True if injection successful, False otherwise
    """
    logger.info(f"Universal payment callback injection for session {session_id}: {status}")
    
    try:
        # Check if we have an active message queue for this session
        if session_id not in _active_message_queues:
            logger.error(f"No active message queue found for payment callback: {session_id}")
            return False
            
        message_queue = _active_message_queues[session_id]
        
        # Create payment status message
        success = status == "success"
        callback_message = {
            "type": "payment_status",
            "success": success,
            "message": message or ("Payment successful" if success else "Payment failed")
        }
        
        # Inject the callback message into the queue
        await message_queue.put(callback_message)
        logger.info(f"Payment callback injected successfully for session {session_id}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error injecting payment callback for session {session_id}: {e}")
        return False


async def handle_payment_callback(session_id: str, status: str, message: str = "") -> bool:
    """
    Universal payment callback handler for all modules.
    Simply calls inject_payment_callback to inject the callback into the message queue.
    
    Args:
        session_id: Session ID to handle callback for
        status: Payment status ("success" or "failed")
        message: Optional message from payment service
        
    Returns:
        bool: True if callback handled successfully, False otherwise
    """
    logger.info(f"Universal payment callback handler for session {session_id}: {status}")
    return await inject_payment_callback(session_id, status, message)


async def payment_process_simple(amount: float) -> bool:
    """
    Universal payment process function.
    Simple function that processes payment and returns success/failure.
    
    Args:
        amount: Payment amount in currency units
        
    Returns:
        bool: True if payment successful, False if failed
    """
    logger.info(f"Starting universal payment process for amount: {amount}")
    
    if amount <= 0:
        logger.info("No payment needed (amount <= 0)")
        return True
    
    try:
        # Prepare payment data (same format as existing code)
        payment_data = {
            "type": "sale",
            "amount": float(amount),
            "variable_symbol": f"payment_{int(datetime.now().timestamp())}"  # Unique identifier
        }
        
        logger.info(f"Processing payment: {payment_data}")
        
        # Get payment service configuration
        payment_service_timeout = device_config.payment_config.get("payment_service_timeout", 30)
        payment_service_url = device_config.payment_config.get("payment_service_url")
        
        if not payment_service_url:
            logger.error("Payment service URL not configured")
            return False
        
        # Send payment request to payment service
        async with httpx.AsyncClient(timeout=payment_service_timeout) as client:
            response = await client.post(
                payment_service_url,
                json=payment_data
            )
            
            if response.status_code == 200:
                logger.info(f"Payment successful: {response.status_code} - {response.text}")
                return True
            else:
                logger.error(f"Payment failed: {response.status_code} - {response.text}")
                return False
                
    except httpx.TimeoutException:
        logger.error(f"Payment service timeout for amount {amount}")
        return False
        
    except Exception as e:
        logger.error(f"Payment error: {e}")
        return False


async def payment_process_with_callbacks(session_id: str, amount: float, message_queue: asyncio.Queue) -> bool:
    """
    Full payment process with WebSocket messages and callback handling.
    Used when payment needs to be integrated with WebSocket flow.
    
    Args:
        session_id: WebSocket session ID
        amount: Payment amount in currency units
        message_queue: Queue to receive WebSocket messages
        
    Returns:
        bool: True if payment successful, False if failed
    """
    logger.info("Payment required, starting payment process")

    # Register message queue for universal payment callback injection
    register_message_queue(session_id, message_queue)

    try:
        # Send start payment screen message
        await ws_manager.send(session_id, {
            "type": "start_payment_screen",
            "wait_for_ready": True
        })

        # Wait for payment_screen_ready
        while True:
            try:
                message = await message_queue.get()
            except Exception as e:
                logger.error(f"Error waiting for payment message: {e}")
                return False

            message_type = message.get("type")
            logger.info(f"Processing payment message: {message_type}")

            if message_type == "payment_screen_ready":
                logger.info("Payment screen ready - starting payment terminal call")

                # Send payment initiation status
                await ws_manager.send(session_id, {
                    "type": "payment_status",
                    "status": "initiating"
                })

                # Call payment terminal (this initiates payment but doesn't wait for result)
                payment_initiated = await payment_process_simple(amount)

                if payment_initiated:
                    # Send payment processing status
                    await ws_manager.send(session_id, {
                        "type": "payment_status",
                        "status": "processing"
                    })
                    logger.info("Payment initiated successfully, waiting for callback")
                    # Continue waiting for payment_status callback - don't break here
                else:
                    # Payment terminal call failed
                    await ws_manager.send(session_id, {
                        "type": "payment_result",
                        "success": False,
                        "message": "Payment failed to initiate"
                    })
                    return False

            elif message_type == "payment_status":
                # Handle payment status from payment callback
                logger.info("Received payment status callback")

                success = message.get("success", False)

                # Send payment result
                await ws_manager.send(session_id, {
                    "type": "payment_result",
                    "success": success,
                    "message": "Payment successful" if success else "Payment failed"
                })

                if success:
                    logger.info("Payment completed successfully")
                    return True
                else:
                    logger.info("Payment failed")
                    return False

            elif message_type == "storno":
                logger.info("Payment cancelled by user")
                return False

    except Exception as e:
        logger.error(f"Error in payment_process: {e}")
        return False
    finally:
        # Clean up universal message queue registration
        unregister_message_queue(session_id)
